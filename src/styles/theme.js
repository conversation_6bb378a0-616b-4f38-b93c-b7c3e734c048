// Theme configuration for consistent styling
export const colors = {
  // Primary colors
  primary: {
    main: '#2196F3',
    light: '#64B5F6',
    dark: '#1976D2',
    contrastText: '#ffffff',
  },
  
  // Secondary colors
  secondary: {
    main: '#4CAF50',
    light: '#81C784',
    dark: '#388E3C',
    contrastText: '#ffffff',
  },
  
  // Error colors
  error: {
    main: '#f44336',
    light: '#ef5350',
    dark: '#c62828',
    background: '#ffebee',
    contrastText: '#ffffff',
  },
  
  // Warning colors
  warning: {
    main: '#ff9800',
    light: '#ffb74d',
    dark: '#f57c00',
    background: '#fff3e0',
    contrastText: '#ffffff',
  },
  
  // Success colors
  success: {
    main: '#4caf50',
    light: '#81c784',
    dark: '#388e3c',
    background: '#e8f5e8',
    contrastText: '#ffffff',
  },
  
  // Neutral colors
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // Background colors
  background: {
    default: '#ffffff',
    paper: '#ffffff',
    dark: '#121212',
    darkPaper: '#1e1e1e',
  },
  
  // Text colors
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#bdbdbd',
    hint: '#9e9e9e',
    primaryDark: '#ffffff',
    secondaryDark: '#aaaaaa',
  },
  
  // Border colors
  border: {
    light: '#e0e0e0',
    main: '#bdbdbd',
    dark: '#757575',
  },
};

export const spacing = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  xxl: '3rem',
};

export const typography = {
  fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  monoFamily: '"SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas, "Courier New", monospace',
  
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
  },
  
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
};

export const borderRadius = {
  none: '0',
  sm: '0.125rem',
  base: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  full: '9999px',
};

export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// Common component styles
export const components = {
  button: {
    base: {
      padding: `${spacing.sm} ${spacing.md}`,
      borderRadius: borderRadius.base,
      border: 'none',
      cursor: 'pointer',
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.medium,
      transition: 'all 0.2s ease-in-out',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: spacing.xs,
    },
    primary: {
      backgroundColor: colors.primary.main,
      color: colors.primary.contrastText,
    },
    secondary: {
      backgroundColor: colors.secondary.main,
      color: colors.secondary.contrastText,
    },
    error: {
      backgroundColor: colors.error.main,
      color: colors.error.contrastText,
    },
    outline: {
      backgroundColor: 'transparent',
      border: `1px solid ${colors.border.main}`,
      color: colors.text.primary,
    },
  },
  
  card: {
    base: {
      backgroundColor: colors.background.paper,
      borderRadius: borderRadius.lg,
      boxShadow: shadows.base,
      padding: spacing.lg,
      marginBottom: spacing.lg,
    },
  },
  
  input: {
    base: {
      padding: spacing.sm,
      border: `1px solid ${colors.border.light}`,
      borderRadius: borderRadius.base,
      fontSize: typography.fontSize.base,
      fontFamily: typography.fontFamily,
      width: '100%',
      transition: 'border-color 0.2s ease-in-out',
    },
    focus: {
      borderColor: colors.primary.main,
      outline: 'none',
      boxShadow: `0 0 0 2px ${colors.primary.main}20`,
    },
  },
  
  textarea: {
    base: {
      padding: spacing.sm,
      border: `1px solid ${colors.border.light}`,
      borderRadius: borderRadius.base,
      fontSize: typography.fontSize.base,
      fontFamily: typography.monoFamily,
      width: '100%',
      minHeight: '300px',
      resize: 'vertical',
      transition: 'border-color 0.2s ease-in-out',
    },
  },
};
