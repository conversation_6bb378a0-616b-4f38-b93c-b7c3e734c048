// Example content for demonstrating the application features
export const getExampleGoalsContent = () => {
  const today = new Date();
  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);
  const nextMonth = new Date(today);
  nextMonth.setMonth(today.getMonth() + 1);
  const nextYear = new Date(today);
  nextYear.setFullYear(today.getFullYear() + 1);

  const formatDate = (date) => date.toISOString().slice(0, 10);

  return `# GOALS

Welcome to your Daily Notes App! This is an example goals file to demonstrate the application's features.

## Current Projects

### Learning & Development
- **React Development**: Building modern web applications
- **Data Visualization**: Creating interactive charts and graphs
- **Personal Productivity**: Optimizing daily workflows

### Health & Wellness
- **Exercise Routine**: Maintaining regular physical activity
- **Mindfulness Practice**: Daily meditation and reflection
- **Nutrition Goals**: Healthy eating habits

# DATA

## Project: React Development
- Task: Complete React Tutorial
  - Start: ${formatDate(today)}
  - Finish: ${formatDate(nextWeek)}
  - Status: In Progress
- Task: Build Portfolio Website
  - Start: ${formatDate(nextWeek)}
  - Finish: ${formatDate(nextMonth)}
  - Status: Planned

## Project: Health & Wellness
- Task: Daily 30min Walk
  - Start: ${formatDate(today)}
  - Finish: ${formatDate(nextMonth)}
  - Status: In Progress
- Task: Weekly Meal Prep
  - Start: ${formatDate(today)}
  - Finish: ${formatDate(nextYear)}
  - Status: In Progress

## Project: Personal Productivity
- Task: Setup Daily Notes System
  - Start: ${formatDate(today)}
  - Finish: ${formatDate(today)}
  - Status: Complete
- Task: Organize Digital Files
  - Start: ${formatDate(nextWeek)}
  - Finish: ${formatDate(nextMonth)}
  - Status: Planned

# NOTES

This goals file demonstrates:
1. **Project Planning**: Organize your goals into projects and tasks
2. **Timeline Visualization**: See your progress on interactive charts
3. **Status Tracking**: Monitor what's complete, in progress, or planned
4. **Data-Driven Insights**: Use the DATA section for timeline charts

## How to Use This App

1. **Select a Folder**: Choose a folder containing your markdown files
2. **Edit Goals**: Click "Edit Goals" to modify your project timeline
3. **Daily Notes**: Create and edit daily notes with markdown support
4. **Visual Timeline**: View your progress in the interactive charts

## Tips for Success

- Use consistent date formats (YYYY-MM-DD) in the DATA section
- Keep your daily notes organized by date
- Review and update your goals regularly
- Use the preview feature when editing notes

Happy note-taking! 📝✨`;
};

export const getExampleDailyNotes = () => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const twoDaysAgo = new Date(today);
  twoDaysAgo.setDate(today.getDate() - 2);

  const formatDate = (date) => date.toISOString().slice(0, 10);

  return [
    {
      name: formatDate(today),
      content: `# Daily Note - ${formatDate(today)}

## Today's Goals
- [x] Set up Daily Notes App
- [ ] Review project timeline
- [ ] Plan tomorrow's tasks

## Accomplishments
- Successfully configured the daily notes system
- Explored the timeline visualization features
- Organized project goals and tasks

## Learning Notes
- The app supports **markdown formatting** with GFM (GitHub Flavored Markdown)
- Timeline charts automatically update based on the goals.md DATA section
- Dark mode toggle provides better visual comfort

## Ideas & Thoughts
- Consider adding tags to notes for better organization
- Weekly review sessions could help track progress
- Integration with calendar apps might be useful

## Tomorrow's Focus
- Complete React tutorial module 3
- Update project timeline with new deadlines
- Review and organize digital files

---
*This is an example daily note showing the app's capabilities!*`
    },
    {
      name: formatDate(yesterday),
      content: `# Daily Note - ${formatDate(yesterday)}

## Today's Goals
- [x] Research daily note-taking systems
- [x] Evaluate different markdown editors
- [ ] Set up project tracking system

## Accomplishments
- Compared various note-taking applications
- Identified key features needed for productivity
- Started planning the daily workflow

## Challenges
- Too many options available - decision paralysis
- Need to balance simplicity with functionality
- Time management while evaluating tools

## Insights
- Simple markdown-based systems are most flexible
- Visual timeline helps with project planning
- Daily reflection improves self-awareness

## Next Steps
- Choose and set up the daily notes system
- Create initial project timeline
- Establish daily review routine`
    },
    {
      name: formatDate(twoDaysAgo),
      content: `# Daily Note - ${formatDate(twoDaysAgo)}

## Today's Goals
- [x] Morning workout routine
- [x] Read productivity articles
- [ ] Organize workspace

## Accomplishments
- Completed 30-minute morning walk
- Read 3 articles on personal productivity
- Cleaned and organized desk area

## Learning Notes
- **Productivity Insight**: Small daily habits compound over time
- **Health**: Morning exercise improves focus throughout the day
- **Organization**: Clean workspace leads to clearer thinking

## Reflections
Today felt productive and focused. The morning routine is starting to become a habit, which is encouraging. Need to work on consistency with the workspace organization.

## Ideas for Tomorrow
- Try the new note-taking system I researched
- Set up a simple project tracking method
- Continue with the morning routine

## Gratitude
- Grateful for good health and energy
- Appreciate having time for learning and growth
- Thankful for a comfortable workspace`
    }
  ];
};

export const hasExistingContent = (goalsMd, dailyNoteNames) => {
  // Check if there's meaningful content beyond default messages
  const hasGoals = goalsMd && 
    goalsMd.trim() !== '' && 
    !goalsMd.includes('Please select a folder') &&
    !goalsMd.includes('No Markdown files found');
    
  const hasNotes = dailyNoteNames && dailyNoteNames.length > 0;
  
  return hasGoals || hasNotes;
};

export const shouldLoadExampleContent = (goalsMd, dailyNoteNames, isFolderSelected) => {
  return isFolderSelected && !hasExistingContent(goalsMd, dailyNoteNames);
};
