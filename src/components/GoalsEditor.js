// components/GoalsEditor.js
import React, { useState, useEffect } from 'react';

const GoalsEditor = ({ initialContent, onSave, onCancel }) => {
  const [projects, setProjects] = useState([]);
  const [editingProject, setEditingProject] = useState(null);
  const [editingTask, setEditingTask] = useState(null);

  // Parse initial content when component mounts
  useEffect(() => {
    parseGoalsContent(initialContent);
  }, [initialContent]);

  const parseGoalsContent = (content) => {
    const lines = content.split('\n');
    const parsedProjects = [];
    let currentProject = null;
    let inDataSection = false;

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // Check if we're entering the DATA section
      if (trimmedLine === '# DATA') {
        inDataSection = true;
        return;
      }

      // Only process content within the DATA section
      if (!inDataSection) return;

      // Stop processing if we hit another main section
      if (trimmedLine.startsWith('# ') && trimmedLine !== '# DATA') {
        inDataSection = false;
        return;
      }

      if (trimmedLine.startsWith('## ')) {
        // New project - save previous project if exists
        if (currentProject) {
          parsedProjects.push(currentProject);
        }
        currentProject = {
          name: trimmedLine.slice(3).trim(),
          color: '',
          tasks: []
        };
      } else if (trimmedLine.startsWith('- ProjectColor:') && currentProject) {
        currentProject.color = trimmedLine.split(':')[1].trim();
      } else if (trimmedLine.startsWith('| ') && currentProject) {
        // Skip table header and separator rows
        if (trimmedLine.includes('| Done | Task |') ||
            trimmedLine.includes('| ---- |') ||
            trimmedLine === '|') {
          return;
        }

        // Parse task row
        const columns = line.split('|').map(col => col.trim());
        if (columns.length >= 6) {
          const task = {
            done: columns[1] || '[ ]',
            task: columns[2] || '',
            start: columns[3] || '',
            finish: columns[4] || '',
            percent: columns[5] || '0'
          };

          // Only add non-empty tasks
          if (task.task.trim()) {
            currentProject.tasks.push(task);
          }
        }
      }
    });

    // Add last project
    if (currentProject) {
      parsedProjects.push(currentProject);
    }

    setProjects(parsedProjects);
  };

  const generateMarkdownContent = () => {
    let content = '# DATA\n\n';

    content += projects.map(project => {
      let projectContent = `## ${project.name}\n`;
      projectContent += `- ProjectColor: ${project.color}\n\n`;
      projectContent += `| Done | Task                                | Start      | Finish     | Percent |\n`;
      projectContent += `| ---- | ----------------------------------- | ---------- | ---------- | ------- |\n`;

      project.tasks.forEach(task => {
        // Ensure proper formatting for table columns
        const done = task.done || '[ ]';
        const taskName = task.task || '';
        const start = task.start || '';
        const finish = task.finish || '';
        const percent = task.percent || '0';

        projectContent += `| ${done} | ${taskName.padEnd(35)} | ${start.padEnd(10)} | ${finish.padEnd(10)} | ${percent.padEnd(7)} |\n`;
      });

      return projectContent + '\n';
    }).join('');

    return content;
  };

  const handleSave = () => {
    const markdownContent = generateMarkdownContent();
    onSave(markdownContent);
  };

  const addProject = () => {
    setProjects([
      ...projects, 
      { 
        name: 'New Project', 
        color: 'gray', 
        tasks: [] 
      }
    ]);
  };

  const addTask = (projectIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks.push({
      done: '[ ]',
      task: 'New Task',
      start: '',
      finish: '',
      percent: '0'
    });
    setProjects(newProjects);
  };

  const updateProject = (projectIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex][field] = value;
    setProjects(newProjects);
  };

  const updateTask = (projectIndex, taskIndex, field, value) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks[taskIndex][field] = value;
    setProjects(newProjects);
  };

  const deleteProject = (projectIndex) => {
    const newProjects = projects.filter((_, index) => index !== projectIndex);
    setProjects(newProjects);
  };

  const deleteTask = (projectIndex, taskIndex) => {
    const newProjects = [...projects];
    newProjects[projectIndex].tasks = newProjects[projectIndex].tasks.filter((_, index) => index !== taskIndex);
    setProjects(newProjects);
  };

  return (
    <div style={{ 
      maxWidth: '1000px', 
      margin: 'auto', 
      padding: '20px', 
      backgroundColor: '#f5f5f5',
      borderRadius: '8px'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        marginBottom: '20px' 
      }}>
        <h2>Goals Editor</h2>
        <div>
          <button 
            onClick={addProject}
            style={{
              marginRight: '10px',
              padding: '5px 10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Add Project
          </button>
          <button 
            onClick={handleSave}
            style={{
              marginRight: '10px',
              padding: '5px 10px',
              backgroundColor: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Save
          </button>
          <button 
            onClick={onCancel}
            style={{
              padding: '5px 10px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px'
            }}
          >
            Cancel
          </button>
        </div>
      </div>

      {projects.map((project, projectIndex) => (
        <div 
          key={projectIndex} 
          style={{ 
            backgroundColor: 'white', 
            marginBottom: '20px', 
            padding: '15px', 
            borderRadius: '8px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
            <input 
              value={project.name}
              onChange={(e) => updateProject(projectIndex, 'name', e.target.value)}
              style={{ 
                flex: 1, 
                marginRight: '10px', 
                padding: '5px',
                fontSize: '1.2em',
                fontWeight: 'bold'
              }}
            />
            <input 
              value={project.color}
              onChange={(e) => updateProject(projectIndex, 'color', e.target.value)}
              placeholder="Project Color"
              style={{ 
                width: '100px', 
                padding: '5px',
                marginRight: '10px'
              }}
            />
            <button 
              onClick={() => deleteProject(projectIndex)}
              style={{
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                padding: '5px 10px'
              }}
            >
              Delete Project
            </button>
          </div>

          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f1f1f1' }}>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Done</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Task</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Start</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Finish</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Percent</th>
                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {project.tasks.map((task, taskIndex) => (
                <tr key={taskIndex}>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <select 
                      value={task.done}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'done', e.target.value)}
                    >
                      <option value="[ ]">Incomplete</option>
                      <option value="[x]">Complete</option>
                    </select>
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      value={task.task}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'task', e.target.value)}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      type="date"
                      value={task.start}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'start', e.target.value)}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      type="date"
                      value={task.finish}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'finish', e.target.value)}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <input 
                      type="number"
                      min="0"
                      max="100"
                      value={task.percent}
                      onChange={(e) => updateTask(projectIndex, taskIndex, 'percent', e.target.value)}
                      style={{ width: '60px' }}
                    />
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'center' }}>
                    <button 
                      onClick={() => deleteTask(projectIndex, taskIndex)}
                      style={{
                        backgroundColor: '#f44336',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '3px 6px'
                      }}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <button 
            onClick={() => addTask(projectIndex)}
            style={{
              marginTop: '10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '5px 10px'
            }}
          >
            Add Task
          </button>
        </div>
      ))}
    </div>
  );
};

export default GoalsEditor;
